pub mod ads_yms_test_item_service;
pub mod cp_ads_yms_test_item_service;

use crate::config::DwTestItemConfig;
pub use ads_yms_test_item_service::AdsYmsTestItemService;
use chrono::{DateTime, Utc};
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::SinkHandler;
pub use cp_ads_yms_test_item_service::CpYmsAdsTestItemService;
use std::error::Error;

/// Execute tombstone operations for multiple handlers
///
/// This method reduces code duplication by handling the common tombstone operation logic
async fn execute_tombstone_operations(
    properties: &DwTestItemConfig,
    handlers: &[Box<dyn SinkHandler>],
    customer: &str,
    factory: &str,
    test_area: &str,
    lot_id: &str,
    lot_type: &str,
    test_stage: &str,
    device_id: &str,
    wafer_no: &str,
    data_source: &str,
    dt: DateTime<Utc>,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    // Execute tombstone operations for each handler
    for handler in handlers {
        let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());
        let partition_expr = handler.partition_expr().replace("{DATA_SOURCE}", data_source);

        CkOperate::tombstone_ck(
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            None, // lot_bucket
            wafer_no,
            &table_full_name,
            &properties.pick_random_ck_node_host(),
            &properties.get_ck_address_list(),
            &properties.ck_username,
            &properties.ck_password,
            &partition_expr,
            Some(dt),
        )
        .await
        .map_err(|e| -> Box<dyn Error + Send + Sync> {
            Box::new(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Tombstone operation failed for table {}: {}", table_full_name, e),
            ))
        })?;
    }
    Ok(())
}
