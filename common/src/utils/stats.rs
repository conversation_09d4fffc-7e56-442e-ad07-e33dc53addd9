use average::Estimate;
use average::{Mean, Variance};

const DOUBLE_MAX: f64 = 99000000000000000000.0;
// 无偏偏度计算
pub fn calculate_skewness(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 3.0 {
        return None; // 样本不足无法计算无偏偏度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum3) = data.iter().fold((0.0, 0.0), |(s2, s3), &x| {
        let dev = x - mean;
        (s2 + dev * dev, s3 + dev * dev * dev)
    });

    // 计算中心矩
    let m2 = sum2 / n;
    let m3 = sum3 / n;

    if m2 <= 1e-12 {
        // 处理极小方差
        return None;
    }

    // 原始偏度计算
    let g1 = m3 / m2.powf(1.5);

    // 无偏修正系数
    let correction = (n * (n - 1.0)).sqrt() / (n - 2.0);

    Some(g1 * correction)
}

// 无偏峰度计算
pub fn calculate_kurtosis(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 4.0 {
        return None; // 样本不足无法计算无偏峰度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum4) = data.iter().fold((0.0, 0.0), |(s2, s4), &x| {
        let dev = x - mean;
        let dev2 = dev * dev;
        (s2 + dev2, s4 + dev2 * dev2)
    });

    // 无偏方差计算
    let variance = sum2 / (n - 1.0);

    if variance == 0.0 {
        return None; // 方差为0无法计算峰度
    }

    // 无偏峰度计算
    let term1 = (n * (n + 1.0)) / ((n - 1.0) * (n - 2.0) * (n - 3.0));
    let term2 = sum4 / (variance.powi(2));
    let term3 = (3.0 * (n - 1.0).powi(2)) / ((n - 2.0) * (n - 3.0));

    Some(term1 * term2 - term3)
}

pub fn calculate_square_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().map(|x| x * x).sum())
}

pub fn calculate_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().sum())
}

pub fn calculate_mean(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut mean = Mean::new();
    data.iter().for_each(|&x| mean.add(x));
    Some(mean.mean())
}

pub fn calculate_std_dev(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut variance = Variance::new();
    data.iter().for_each(|&x| variance.add(x));
    Some(variance.estimate().sqrt())
}

pub fn calculate_quantile(data: &[f64], p: f64) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut sorted_data = data.to_vec();
    sorted_data.sort_by(|a, b| a.partial_cmp(b).unwrap());

    let n = data.len();
    let px = p / 100.0 * (n - 1) as f64;
    let i = px.floor() as usize;
    let g = px - i as f64;

    if g == 0.0 {
        Some(sorted_data[i])
    } else {
        Some((1.0 - g) * sorted_data[i] + g * sorted_data[i + 1])
    }
}

pub fn calculate_cp(std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        DOUBLE_MAX
    } else {
        divide(high_limit - low_limit, 6.0 * std_dev)
    }
}

pub fn calculate_cpk(mean: f64, std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    calculate_cpu(mean, std_dev, high_limit).min(calculate_cpl(mean, std_dev, low_limit))
}

pub fn calculate_cpu(mean: f64, std_dev: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        DOUBLE_MAX
    } else {
        divide(high_limit - mean, 3.0 * std_dev)
    }
}

pub fn calculate_cpl(mean: f64, std_dev: f64, low_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        DOUBLE_MAX
    } else {
        divide(mean - low_limit, 3.0 * std_dev)
    }
}

pub fn calculate_ca(mean: f64, low_limit: f64, high_limit: f64) -> f64 {
    let target = high_limit + low_limit;
    let tolerance = high_limit - low_limit;

    if equals(tolerance, 0.0) {
        0.0
    } else {
        divide((mean * 2.0 - target).abs(), tolerance)
    }
}

// 辅助函数，用于保持与Scala版本一致的精度
fn equals(a: f64, b: f64) -> bool {
    (a - b).abs() < std::f64::EPSILON
}

pub fn divide(a: f64, b: f64) -> f64 {
    // 这里要跟scala版本的精度保持一致
    divide_with_scale(a, b, 6)
}

pub fn divide_with_scale(a: f64, b: f64, scale: i32) -> f64 {
    if equals(b, 0.0) {
        0.0
    } else {
        let scale_factor = 10.0_f64.powi(scale);
        (a / b * scale_factor).round() / scale_factor
    }
}

/// 计算最大值
pub fn calculate_max(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    data.iter().max_by(|a, b| a.partial_cmp(b).unwrap()).copied()
}

/// 计算最小值
pub fn calculate_min(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    data.iter().min_by(|a, b| a.partial_cmp(b).unwrap()).copied()
}

/// 计算范围（最大值 - 最小值）
pub fn calculate_range(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let max_val = calculate_max(data)?;
    let min_val = calculate_min(data)?;
    Some(max_val - min_val)
}

/// 计算第一四分位数 (Q1)
pub fn calculate_q1(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 25.0)
}

/// 计算第三四分位数 (Q3)
pub fn calculate_q3(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 75.0)
}

/// 计算四分位距 (IQR = Q3 - Q1)
pub fn calculate_iqr(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let q1 = calculate_q1(data)?;
    let q3 = calculate_q3(data)?;
    Some(q3 - q1)
}

/// 计算中位数
pub fn calculate_median(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 50.0)
}

// Specific quantile functions as required by the ADS YMS migration
/// 计算第1百分位数
pub fn quantile1(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 1.0)
}

/// 计算第5百分位数
pub fn quantile5(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 5.0)
}

/// 计算第10百分位数
pub fn quantile10(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 10.0)
}

/// 计算第25百分位数 (Q1)
pub fn quantile25(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 25.0)
}

/// 计算第50百分位数 (中位数)
pub fn quantile50(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 50.0)
}

/// 计算第75百分位数 (Q3)
pub fn quantile75(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 75.0)
}

/// 计算第90百分位数
pub fn quantile90(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 90.0)
}

/// 计算第95百分位数
pub fn quantile95(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 95.0)
}

/// 计算第99百分位数
pub fn quantile99(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 99.0)
}

/// 安全的小数处理函数，对应Scala中的CommonUtil.safeDecimal
/// 处理无穷大、NaN等特殊值，返回Option<f64>
pub fn safe_decimal(value: f64) -> Option<f64> {
    if value.is_finite() && !value.is_nan() {
        Some(value)
    } else {
        None
    }
}

/// 安全计算CP值，如果出现异常（如std为0）则返回None
pub fn calculate_cp_safe(std_dev: Option<f64>, lsl: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(std_dev), Some(lsl), Some(usl)) = (std_dev, lsl, usl) {
        // 当标准差为0时，CP值无意义，返回None
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cp(std_dev, lsl, usl))
    } else {
        None
    }
}

/// 安全计算CPK值，如果出现异常（如std为0）则返回None
pub fn calculate_cpk_safe(mean: Option<f64>, std_dev: Option<f64>, lsl: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(mean), Some(std_dev), Some(lsl), Some(usl)) = (mean, std_dev, lsl, usl) {
        // 当标准差为0时，CPK值无意义，返回None
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cpk(mean, std_dev, lsl, usl))
    } else {
        None
    }
}

/// 安全计算CPU值，如果出现异常（如std为0）则返回None
pub fn calculate_cpu_safe(mean: Option<f64>, std_dev: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(mean), Some(std_dev), Some(usl)) = (mean, std_dev, usl) {
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cpu(mean, std_dev, usl))
    } else {
        None
    }
}

/// 安全计算CPL值，如果出现异常（如std为0）则返回None
pub fn calculate_cpl_safe(mean: Option<f64>, std_dev: Option<f64>, lsl: Option<f64>) -> Option<f64> {
    if let (Some(mean), Some(std_dev), Some(lsl)) = (mean, std_dev, lsl) {
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cpl(mean, std_dev, lsl))
    } else {
        None
    }
}

/// 安全计算CA值，如果出现异常则返回None
pub fn calculate_ca_safe(mean: Option<f64>, lsl: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(mean), Some(lsl), Some(usl)) = (mean, lsl, usl) {
        Some(calculate_ca(mean, lsl, usl))
    } else {
        None
    }
}

// Outlier detection and filtering functions

/// 计算异常值检测的边界值，使用IQR方法
/// 返回 (lower_bound, upper_bound)
/// 使用公式: (2.5*Q1 - 1.5*Q3, 2.5*Q3 - 1.5*Q1)
pub fn calculate_outlier_bounds(data: &[f64]) -> Option<(f64, f64)> {
    if data.is_empty() {
        return None;
    }

    let q1 = calculate_q1(data)?;
    let q3 = calculate_q3(data)?;

    // 使用任务中指定的公式: (2.5*Q1 - 1.5*Q3, 2.5*Q3 - 1.5*Q1)
    let lower_bound = 2.5 * q1 - 1.5 * q3;
    let upper_bound = 2.5 * q3 - 1.5 * q1;

    Some((lower_bound, upper_bound))
}

/// 检测数据中的异常值
/// 返回异常值的索引列表
pub fn detect_outliers(data: &[f64]) -> Vec<usize> {
    if let Some((lower_bound, upper_bound)) = calculate_outlier_bounds(data) {
        data.iter()
            .enumerate()
            .filter_map(|(i, &value)| if value < lower_bound || value > upper_bound { Some(i) } else { None })
            .collect()
    } else {
        Vec::new()
    }
}

/// 过滤掉异常值，返回过滤后的数据
pub fn filter_outliers(data: &[f64]) -> Vec<f64> {
    if let Some((lower_bound, upper_bound)) = calculate_outlier_bounds(data) {
        data.iter()
            .filter(|&&value| value >= lower_bound && value <= upper_bound)
            .copied()
            .collect()
    } else {
        data.to_vec()
    }
}

/// 检查单个值是否为异常值
pub fn is_outlier(value: f64, data: &[f64]) -> bool {
    if let Some((lower_bound, upper_bound)) = calculate_outlier_bounds(data) {
        value < lower_bound || value > upper_bound
    } else {
        false
    }
}

/// 标准化数值，将数值转换为标准分数 (z-score)
pub fn normalize_value(value: f64, mean: f64, std_dev: f64) -> Option<f64> {
    if std_dev == 0.0 {
        None
    } else {
        Some((value - mean) / std_dev)
    }
}

/// 对整个数据集进行标准化
pub fn normalize_dataset(data: &[f64]) -> Option<Vec<f64>> {
    if data.is_empty() {
        return Some(Vec::new());
    }

    let mean = calculate_mean(data)?;
    let std_dev = calculate_std_dev(data)?;

    if std_dev == 0.0 {
        // 如果标准差为0，所有值都相同，返回全0向量
        return Some(vec![0.0; data.len()]);
    }

    let normalized: Vec<f64> = data.iter().map(|&value| (value - mean) / std_dev).collect();

    Some(normalized)
}

/// 安全的异常值过滤，处理空数据和异常情况
pub fn filter_outliers_safe(data: &[f64]) -> Vec<f64> {
    if data.is_empty() {
        return Vec::new();
    }

    // 如果数据量太少，不进行异常值过滤
    if data.len() < 4 {
        return data.to_vec();
    }

    filter_outliers(data)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cp_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        // 计算标准差
        let std_dev = calculate_std_dev(&values).unwrap();
        println!("Standard Deviation: {}", std_dev);

        // 计算 Cp
        let cp = calculate_cp(std_dev, low_limit, high_limit);
        println!("Cp: {}", cp);

        // 打印中间计算步骤
        let range = high_limit - low_limit;
        println!("Range (high - low): {}", range);
        let denominator = 6.0 * std_dev;
        println!("Denominator (6 * std_dev): {}", denominator);
    }

    #[test]
    fn test_cpk_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        let std_dev = calculate_std_dev(&values).unwrap();
        let mean = calculate_mean(&values).unwrap();

        println!("Mean: {}", mean);
        println!("Standard Deviation: {}", std_dev);

        let cpu = calculate_cpu(mean, std_dev, high_limit);
        let cpl = calculate_cpl(mean, std_dev, low_limit);
        let cpk = calculate_cpk(mean, std_dev, low_limit, high_limit);

        println!("CPU: {}", cpu);
        println!("CPL: {}", cpl);
        println!("Cpk: {}", cpk);

        // 打印中间计算步骤
        println!("CPU calculation:");
        println!("  (high_limit - mean): {}", high_limit - mean);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);

        println!("CPL calculation:");
        println!("  (mean - low_limit): {}", mean - low_limit);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);
    }

    #[test]
    fn test_divide_with_scale() {
        assert_eq!(divide_with_scale(10.0, 3.0, 1), 3.3);
        assert_eq!(divide_with_scale(10.0, 3.0, 2), 3.33);
        assert_eq!(divide_with_scale(10.0, 3.0, 3), 3.333);
        assert_eq!(divide_with_scale(10.0, 0.0, 2), 0.0);
    }

    #[test]
    fn test_divide() {
        // 测试正常除法
        assert_eq!(divide(10.0, 2.0), 5.0);

        // 测试除零情况
        assert_eq!(divide(10.0, 0.0), 0.0);

        // 测试精度（默认6位小数）
        let result = divide(10.0, 3.0);
        assert_eq!(result, 3.333333);

        // 测试失败率计算场景
        assert_eq!(divide(5.0, 10.0), 0.5); // 50% 失败率
        assert_eq!(divide(0.0, 10.0), 0.0); // 0% 失败率
        assert_eq!(divide(3.0, 0.0), 0.0); // 除零情况
    }

    #[test]
    fn test_basic_stats() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];

        // 测试基本统计函数
        assert_eq!(calculate_max(&data), Some(10.0));
        assert_eq!(calculate_min(&data), Some(1.0));
        assert_eq!(calculate_range(&data), Some(9.0));
        assert_eq!(calculate_mean(&data).unwrap(), 5.5);

        // 测试四分位数
        assert_eq!(calculate_q1(&data), Some(3.25));
        assert_eq!(calculate_q3(&data), Some(7.75));
        assert_eq!(calculate_iqr(&data), Some(4.5));
        assert_eq!(calculate_median(&data), Some(5.5));

        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        assert_eq!(calculate_max(&empty_data), None);
        assert_eq!(calculate_min(&empty_data), None);
        assert_eq!(calculate_range(&empty_data), None);
        assert_eq!(calculate_mean(&empty_data), None);
        assert_eq!(calculate_q1(&empty_data), None);
        assert_eq!(calculate_q3(&empty_data), None);
        assert_eq!(calculate_iqr(&empty_data), None);
        assert_eq!(calculate_median(&empty_data), None);
    }

    #[test]
    fn test_safe_decimal() {
        // 测试正常值
        assert_eq!(safe_decimal(1.5), Some(1.5));
        assert_eq!(safe_decimal(0.0), Some(0.0));
        assert_eq!(safe_decimal(-1.5), Some(-1.5));

        // 测试特殊值
        assert_eq!(safe_decimal(f64::INFINITY), None);
        assert_eq!(safe_decimal(f64::NEG_INFINITY), None);
        assert_eq!(safe_decimal(f64::NAN), None);
    }

    #[test]
    fn test_calculate_cp_safe() {
        // 测试正常情况
        let result = calculate_cp_safe(Some(0.5), Some(1.0), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);

        // 测试缺少参数的情况
        assert_eq!(calculate_cp_safe(None, Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cp_safe(Some(0.5), None, Some(10.0)), None);
        assert_eq!(calculate_cp_safe(Some(0.5), Some(1.0), None), None);

        // 测试标准差为0的情况
        let result_zero_std = calculate_cp_safe(Some(0.0), Some(1.0), Some(10.0));
        assert!(result_zero_std.is_none()); // 因为会产生无穷大值
    }

    #[test]
    fn test_calculate_cpk_safe() {
        // 测试正常情况
        let result = calculate_cpk_safe(Some(5.0), Some(0.5), Some(1.0), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);

        // 测试缺少参数的情况
        assert_eq!(calculate_cpk_safe(None, Some(0.5), Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), None, Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), Some(0.5), None, Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), Some(0.5), Some(1.0), None), None);

        // 测试标准差为0的情况
        let result_zero_std = calculate_cpk_safe(Some(5.0), Some(0.0), Some(1.0), Some(10.0));
        assert!(result_zero_std.is_none()); // 因为会产生无穷大值
    }

    #[test]
    fn test_quantile_functions() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];

        // 测试各个百分位数函数
        assert!(quantile1(&data).is_some());
        assert!(quantile5(&data).is_some());
        assert!(quantile10(&data).is_some());
        assert_eq!(quantile25(&data), Some(3.25));
        assert_eq!(quantile50(&data), Some(5.5));
        assert_eq!(quantile75(&data), Some(7.75));
        assert!(quantile90(&data).is_some());
        assert!(quantile95(&data).is_some());
        assert!(quantile99(&data).is_some());

        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        assert_eq!(quantile1(&empty_data), None);
        assert_eq!(quantile50(&empty_data), None);
        assert_eq!(quantile99(&empty_data), None);
    }

    #[test]
    fn test_calculate_ca() {
        // 测试正常情况
        let ca = calculate_ca(5.5, 1.0, 10.0);
        assert!(ca >= 0.0);

        // 测试目标值在中心的情况 (target = (10+1)/2 = 5.5, tolerance = (10-1)/2 = 4.5)
        let ca_center = calculate_ca(5.5, 1.0, 10.0);
        assert_eq!(ca_center, 0.0); // (5.5 - 5.5) / 4.5 = 0

        // 测试偏离中心的情况
        let ca_off_center = calculate_ca(6.5, 1.0, 10.0);
        assert_eq!(ca_off_center, divide(1.0, 4.5)); // (6.5 - 5.5) / 4.5

        // 测试边界情况
        let ca_boundary = calculate_ca(1.0, 1.0, 10.0);
        assert_eq!(ca_boundary, 1.0); // (1.0 - 5.5) / 4.5 = 1.0
    }

    #[test]
    fn test_calculate_ca_safe() {
        // 测试正常情况
        let result = calculate_ca_safe(Some(5.5), Some(1.0), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() >= 0.0);

        // 测试缺少参数的情况
        assert_eq!(calculate_ca_safe(None, Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_ca_safe(Some(5.5), None, Some(10.0)), None);
        assert_eq!(calculate_ca_safe(Some(5.5), Some(1.0), None), None);
    }

    #[test]
    fn test_calculate_cpu_safe() {
        // 测试正常情况
        let result = calculate_cpu_safe(Some(5.0), Some(0.5), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);

        // 测试缺少参数的情况
        assert_eq!(calculate_cpu_safe(None, Some(0.5), Some(10.0)), None);
        assert_eq!(calculate_cpu_safe(Some(5.0), None, Some(10.0)), None);
        assert_eq!(calculate_cpu_safe(Some(5.0), Some(0.5), None), None);

        // 测试标准差为0的情况
        assert_eq!(calculate_cpu_safe(Some(5.0), Some(0.0), Some(10.0)), None);
    }

    #[test]
    fn test_calculate_cpl_safe() {
        // 测试正常情况
        let result = calculate_cpl_safe(Some(5.0), Some(0.5), Some(1.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);

        // 测试缺少参数的情况
        assert_eq!(calculate_cpl_safe(None, Some(0.5), Some(1.0)), None);
        assert_eq!(calculate_cpl_safe(Some(5.0), None, Some(1.0)), None);
        assert_eq!(calculate_cpl_safe(Some(5.0), Some(0.5), None), None);

        // 测试标准差为0的情况
        assert_eq!(calculate_cpl_safe(Some(5.0), Some(0.0), Some(1.0)), None);
    }

    #[test]
    fn test_outlier_detection() {
        // 测试数据：包含明显异常值
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 100.0]; // 100.0 是异常值

        // 测试异常值边界计算
        let bounds = calculate_outlier_bounds(&data);
        assert!(bounds.is_some());
        let (lower_bound, upper_bound) = bounds.unwrap();
        println!("Outlier bounds: ({}, {})", lower_bound, upper_bound);

        // 测试异常值检测
        let outlier_indices = detect_outliers(&data);
        assert!(!outlier_indices.is_empty());
        assert!(outlier_indices.contains(&9)); // 索引9对应值100.0

        // 测试单个值异常检测
        assert!(is_outlier(100.0, &data));
        assert!(!is_outlier(5.0, &data));

        // 测试异常值过滤
        let filtered = filter_outliers(&data);
        assert!(filtered.len() < data.len());
        assert!(!filtered.contains(&100.0));

        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        assert_eq!(calculate_outlier_bounds(&empty_data), None);
        assert_eq!(detect_outliers(&empty_data), Vec::<usize>::new());
        assert_eq!(filter_outliers(&empty_data), Vec::<f64>::new());
    }

    #[test]
    fn test_normalization() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0];

        // 测试单个值标准化
        let mean = calculate_mean(&data).unwrap();
        let std_dev = calculate_std_dev(&data).unwrap();

        let normalized_value = normalize_value(3.0, mean, std_dev);
        assert!(normalized_value.is_some());
        assert!((normalized_value.unwrap() - 0.0).abs() < 1e-10); // 中位数标准化后应该接近0

        // 测试数据集标准化
        let normalized_data = normalize_dataset(&data);
        assert!(normalized_data.is_some());
        let normalized = normalized_data.unwrap();
        assert_eq!(normalized.len(), data.len());

        // 标准化后的数据均值应该接近0
        let normalized_mean = calculate_mean(&normalized).unwrap();
        assert!((normalized_mean - 0.0).abs() < 1e-10);

        // 测试标准差为0的情况
        let constant_data = vec![5.0, 5.0, 5.0, 5.0];
        let normalized_constant = normalize_dataset(&constant_data);
        assert!(normalized_constant.is_some());
        let normalized = normalized_constant.unwrap();
        assert!(normalized.iter().all(|&x| x == 0.0));

        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        let normalized_empty = normalize_dataset(&empty_data);
        assert!(normalized_empty.is_some());
        assert!(normalized_empty.unwrap().is_empty());
    }

    #[test]
    fn test_filter_outliers_safe() {
        // 测试正常数据
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 100.0];
        let filtered = filter_outliers_safe(&data);
        assert!(filtered.len() <= data.len());

        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        let filtered_empty = filter_outliers_safe(&empty_data);
        assert!(filtered_empty.is_empty());

        // 测试数据量太少的情况
        let small_data = vec![1.0, 2.0, 3.0];
        let filtered_small = filter_outliers_safe(&small_data);
        assert_eq!(filtered_small, small_data); // 应该返回原数据
    }
}
