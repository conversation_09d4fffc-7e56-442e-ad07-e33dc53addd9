use crate::dto::ads::value::die_final_info::DieFinalInfo;
use crate::dto::dwd::file_detail::FileDetail;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::model::constant::{HBIN_NAME_PREFIX, MIDDLE_LINE, SBIN_NAME_PREFIX};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// ADS layer TestItemDetail structure for YMS analytics
/// This represents the transformed test item data used for statistical aggregation
/// Based on the DWD test_item_detail structure but simplified for analytics
#[derive(Debug, Clone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemDetail {
    // Upload and customer information
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,

    // Factory and location information
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,

    // Product and lot information
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,

    // Site information
    pub SITE: Option<u32>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: Arc<str>,

    // Bin information
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NAM: Arc<str>,
    pub SBIN_PF: Arc<str>,

    // Test limits and units
    pub UNITS: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT: Option<f64>,

    // Process and equipment information
    pub PROCESS: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,

    // ECID information
    pub ECID: Arc<str>,
    pub IS_STANDARD_ECID: Option<u8>,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TEST_RESULT: Option<u8>,
    pub TEST_VALUE: Option<f64>,
    pub TESTITEM_TYPE: Arc<str>,

    // Processing flags
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,

    // System fields
    pub VERSION: i64,
    pub UPLOAD_TIME: i64,
    pub CREATE_TIME: i64,

    // Timestamps
    pub START_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_TIME: Option<i64>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
}

impl TestItemDetail {
    /// Creates a new TestItemDetail with default values
    pub fn new() -> Self {
        let now = chrono::Utc::now().timestamp_millis();
        Self {
            UPLOAD_TYPE: Arc::from(""),
            CUSTOMER: Arc::from(""),
            SUB_CUSTOMER: Arc::from(""),
            FAB: Arc::from(""),
            FAB_SITE: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            LOT_TYPE: Arc::from(""),
            LOT_ID: Arc::from(""),
            SBLOT_ID: Arc::from(""),
            WAFER_LOT_ID: Arc::from(""),
            WAFER_ID: Arc::from(""),
            WAFER_NO: Arc::from(""),
            TEST_PROGRAM: Arc::from(""),
            TEST_PROGRAM_VERSION: Arc::from(""),
            SITE: None,
            SITE_CNT: None,
            SITE_NUMS: Arc::from(""),
            HBIN: Arc::from(""),
            SBIN: Arc::from(""),
            HBIN_NUM: None,
            HBIN_NAM: Arc::from(""),
            HBIN_PF: Arc::from(""),
            SBIN_NUM: None,
            SBIN_NAM: Arc::from(""),
            SBIN_PF: Arc::from(""),
            UNITS: Arc::from(""),
            ORIGIN_UNITS: Arc::from(""),
            LO_LIMIT: None,
            HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            ORIGIN_HI_LIMIT: None,
            PROCESS: Arc::from(""),
            TEST_TEMPERATURE: Arc::from(""),
            TESTER_NAME: Arc::from(""),
            TESTER_TYPE: Arc::from(""),
            PROBER_HANDLER_ID: Arc::from(""),
            PROBECARD_LOADBOARD_ID: Arc::from(""),
            PROBER_HANDLER_TYP: Arc::from(""),
            PROBECARD_LOADBOARD_TYP: Arc::from(""),
            RETEST_BIN_NUM: Arc::from(""),
            ECID: Arc::from(""),
            IS_STANDARD_ECID: None,
            TEST_NUM: None,
            TEST_TXT: Arc::from(""),
            TEST_ITEM: Arc::from(""),
            TEST_RESULT: None,
            TEST_VALUE: None,
            TESTITEM_TYPE: Arc::from(""),
            IS_FINAL_TEST_IGNORE_TP: None,
            VERSION: 1,
            UPLOAD_TIME: now,
            CREATE_TIME: now,
            START_TIME: None,
            START_HOUR_KEY: Arc::from(""),
            START_DAY_KEY: Arc::from(""),
            END_TIME: None,
            END_HOUR_KEY: Arc::from(""),
            END_DAY_KEY: Arc::from(""),
        }
    }

    /// Check if this is a pass result
    pub fn is_pass(&self) -> bool {
        self.TEST_RESULT == Some(1)
    }

    /// Check if this is a fail result
    pub fn is_fail(&self) -> bool {
        self.TEST_RESULT == Some(0)
    }

    /// Check if this is a pass bin (HBIN_PF == "P")
    pub fn is_pass_bin(&self) -> bool {
        self.HBIN_PF.as_ref() == "P"
    }

    /// Check if this is a fail bin (HBIN_PF == "F")
    pub fn is_fail_bin(&self) -> bool {
        self.HBIN_PF.as_ref() == "F"
    }

    /// Get the bin identifier string (combines HBIN and SBIN)
    pub fn bin_identifier(&self) -> String {
        if !self.HBIN.is_empty() && !self.SBIN.is_empty() {
            format!("H{}_S{}", self.HBIN, self.SBIN)
        } else if !self.HBIN.is_empty() {
            format!("H{}", self.HBIN)
        } else if !self.SBIN.is_empty() {
            format!("S{}", self.SBIN)
        } else {
            "UNKNOWN".to_string()
        }
    }

    /// Check if both HBIN and SBIN are defined
    pub fn has_complete_bin_info(&self) -> bool {
        !self.HBIN.is_empty() && !self.SBIN.is_empty()
    }

    /// Check if this is a standard ECID
    pub fn is_standard_ecid(&self) -> bool {
        self.IS_STANDARD_ECID == Some(1)
    }

    /// Get site numbers from the SITE_NUMS string
    pub fn get_site_numbers(&self) -> Vec<u32> {
        if self.SITE_NUMS.is_empty() {
            return Vec::new();
        }

        self.SITE_NUMS.split(',').filter_map(|s| s.trim().parse::<u32>().ok()).collect()
    }

    /// Get retest bin numbers from the RETEST_BIN_NUM string
    pub fn get_retest_bin_numbers(&self) -> Vec<u32> {
        if self.RETEST_BIN_NUM.is_empty() {
            return Vec::new();
        }

        self.RETEST_BIN_NUM
            .split(',')
            .filter_map(|s| s.trim().parse::<u32>().ok())
            .collect()
    }

    /// Check if a specific retest bin number is included
    pub fn contains_retest_bin(&self, bin_num: u32) -> bool {
        self.get_retest_bin_numbers().contains(&bin_num)
    }

    /// Build TestItemDetail from TestItemDetailRow and FileDetail
    /// This is the standard build method without die final info override
    pub fn build_test_item(test_item_detail: &SubTestItemDetail, file_detail: &FileDetail, version: i64) -> Self {
        let now = chrono::Utc::now().timestamp_millis();

        // Handle HBIN construction: if HBIN is empty/null, construct from HBIN_NUM and HBIN_NAM
        let hbin = if test_item_detail.HBIN.as_ref().map_or(true, |s| s.is_empty()) {
            if let (Some(hbin_num), Some(hbin_nam)) = (test_item_detail.HBIN_NUM, &test_item_detail.HBIN_NAM) {
                Arc::from(format!("{}{}{}{}", HBIN_NAME_PREFIX, hbin_num, MIDDLE_LINE, hbin_nam))
            } else {
                Arc::from(test_item_detail.HBIN.as_deref().unwrap_or(""))
            }
        } else {
            Arc::from(test_item_detail.HBIN.as_deref().unwrap_or(""))
        };

        // Handle SBIN construction: if SBIN is empty/null, construct from SBIN_NUM and SBIN_NAM
        let sbin = if test_item_detail.SBIN.as_ref().map_or(true, |s| s.is_empty()) {
            if let (Some(sbin_num), Some(sbin_nam)) = (test_item_detail.SBIN_NUM, &test_item_detail.SBIN_NAM) {
                Arc::from(format!("{}{}{}{}", SBIN_NAME_PREFIX, sbin_num, MIDDLE_LINE, sbin_nam))
            } else {
                Arc::from(test_item_detail.SBIN.as_deref().unwrap_or(""))
            }
        } else {
            Arc::from(test_item_detail.SBIN.as_deref().unwrap_or(""))
        };

        Self {
            UPLOAD_TYPE: Arc::from(file_detail.UPLOAD_TYPE.as_str()),
            CUSTOMER: Arc::from(file_detail.CUSTOMER.as_str()),
            SUB_CUSTOMER: Arc::from(file_detail.SUB_CUSTOMER.as_str()),
            FAB: Arc::from(file_detail.FAB.as_str()),
            FAB_SITE: Arc::from(file_detail.FAB_SITE.as_str()),
            FACTORY: Arc::from(file_detail.FACTORY.as_str()),
            FACTORY_SITE: Arc::from(file_detail.FACTORY_SITE.as_str()),
            TEST_AREA: Arc::from(file_detail.TEST_AREA.as_str()),
            TEST_STAGE: Arc::from(file_detail.TEST_STAGE.as_str()),
            DEVICE_ID: Arc::from(file_detail.DEVICE_ID.as_str()),
            LOT_TYPE: Arc::from(file_detail.LOT_TYPE.as_str()),
            LOT_ID: Arc::from(file_detail.LOT_ID.as_str()),
            SBLOT_ID: Arc::from(file_detail.SBLOT_ID.as_str()),
            WAFER_LOT_ID: Arc::from(test_item_detail.WAFER_LOT_ID.as_deref().unwrap_or("")),
            WAFER_ID: Arc::from(test_item_detail.WAFER_ID.as_deref().unwrap_or("")),
            WAFER_NO: Arc::from(test_item_detail.WAFER_NO.as_deref().unwrap_or("")),
            TEST_PROGRAM: Arc::from(file_detail.TEST_PROGRAM.as_str()),
            TEST_PROGRAM_VERSION: Arc::from(file_detail.TEST_PROGRAM_VERSION.as_str()),
            SITE: test_item_detail.SITE.map(|v| v as u32),
            SITE_CNT: file_detail.SITE_CNT,
            SITE_NUMS: Arc::from(file_detail.SITE_NUMS.as_str()),
            HBIN: hbin,
            SBIN: sbin,
            HBIN_NUM: test_item_detail.HBIN_NUM.map(|v| v as u32),
            HBIN_NAM: Arc::from(test_item_detail.HBIN_NAM.as_deref().unwrap_or("")),
            HBIN_PF: Arc::from(test_item_detail.HBIN_PF.as_deref().unwrap_or("")),
            SBIN_NUM: test_item_detail.SBIN_NUM.map(|v| v as u32),
            SBIN_NAM: Arc::from(test_item_detail.SBIN_NAM.as_deref().unwrap_or("")),
            SBIN_PF: Arc::from(test_item_detail.SBIN_PF.as_deref().unwrap_or("")),
            UNITS: Arc::from(test_item_detail.UNITS.as_deref().unwrap_or("")),
            ORIGIN_UNITS: Arc::from(test_item_detail.ORIGIN_UNITS.as_deref().unwrap_or("")),
            LO_LIMIT: test_item_detail.LO_LIMIT,
            HI_LIMIT: test_item_detail.HI_LIMIT,
            ORIGIN_LO_LIMIT: test_item_detail.ORIGIN_LO_LIMIT,
            ORIGIN_HI_LIMIT: test_item_detail.ORIGIN_HI_LIMIT,
            PROCESS: Arc::from(file_detail.PROCESS.as_deref().unwrap_or("")),
            TEST_TEMPERATURE: Arc::from(file_detail.TEST_TEMPERATURE.as_str()),
            TESTER_NAME: Arc::from(file_detail.TESTER_NAME.as_str()),
            TESTER_TYPE: Arc::from(file_detail.TESTER_TYPE.as_str()),
            PROBER_HANDLER_ID: Arc::from(file_detail.PROBER_HANDLER_ID.as_str()),
            PROBECARD_LOADBOARD_ID: Arc::from(file_detail.PROBECARD_LOADBOARD_ID.as_str()),
            PROBER_HANDLER_TYP: Arc::from(file_detail.PROBER_HANDLER_TYP.as_str()),
            PROBECARD_LOADBOARD_TYP: Arc::from(file_detail.PROBECARD_LOADBOARD_TYP.as_str()),
            RETEST_BIN_NUM: Arc::from(file_detail.RETEST_BIN_NUM.as_str()),
            ECID: Arc::from(test_item_detail.ECID.as_deref().unwrap_or("")),
            IS_STANDARD_ECID: test_item_detail.IS_STANDARD_ECID.map(|v| v as u8),
            TEST_NUM: test_item_detail.TEST_NUM.map(|v| v as u32),
            TEST_TXT: Arc::from(test_item_detail.TEST_TXT.as_deref().unwrap_or("")),
            TEST_ITEM: Arc::from(test_item_detail.TEST_ITEM.as_deref().unwrap_or("")),
            TEST_RESULT: test_item_detail.TEST_RESULT.map(|v| v as u8),
            TEST_VALUE: test_item_detail.TEST_VALUE,
            TESTITEM_TYPE: Arc::from(test_item_detail.TESTITEM_TYPE.as_deref().unwrap_or("")),
            IS_FINAL_TEST_IGNORE_TP: test_item_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            VERSION: version,
            UPLOAD_TIME: file_detail.UPLOAD_TIME.map(|dt| dt.timestamp_millis()).unwrap_or(now),
            CREATE_TIME: test_item_detail.CREATE_TIME,
            START_TIME: file_detail.START_TIME.map(|dt| dt.timestamp_millis()),
            START_HOUR_KEY: Arc::from(file_detail.START_HOUR_KEY.as_str()),
            START_DAY_KEY: Arc::from(file_detail.START_DAY_KEY.as_str()),
            END_TIME: file_detail.END_TIME.map(|dt| dt.timestamp_millis()),
            END_HOUR_KEY: Arc::from(file_detail.END_HOUR_KEY.as_str()),
            END_DAY_KEY: Arc::from(file_detail.END_DAY_KEY.as_str()),
        }
    }

    /// Build TestItemDetail with DieFinalInfo override
    /// This method looks up die final info to override IS_FINAL_TEST_IGNORE_TP
    pub fn build_test_item_with_die_final_info(
        test_item_detail: &SubTestItemDetail,
        file_detail: &FileDetail,
        version: i64,
        die_final_info: &[DieFinalInfo],
    ) -> Self {
        // Find matching die final info based on ECID and C_PART_ID
        let is_final_test_ignore_tp = die_final_info
            .iter()
            .find(|info| {
                info.ECID.as_ref() == test_item_detail.ECID.as_ref().map(|s| s.as_str()).unwrap_or("")
                    && info.C_PART_ID == test_item_detail.C_PART_ID.map(|v| v as u32)
            })
            .and_then(|info| info.IS_FINAL_TEST_IGNORE_TP)
            .or(test_item_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8));

        // Build the base test item detail
        let mut test_item = Self::build_test_item(test_item_detail, file_detail, version);

        // Override the IS_FINAL_TEST_IGNORE_TP with die final info
        test_item.IS_FINAL_TEST_IGNORE_TP = is_final_test_ignore_tp;

        test_item
    }
}

impl Default for TestItemDetail {
    fn default() -> Self {
        Self::new()
    }
}
